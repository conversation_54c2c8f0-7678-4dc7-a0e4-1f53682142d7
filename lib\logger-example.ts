/**
 * Example usage of the custom logger
 * This file demonstrates how to use the logger in different scenarios
 */

import logger from './logger';

// Example 1: Basic logging
export function basicLoggingExample() {
  logger.log('Application started');
  logger.info('User logged in', { userId: '123', email: '<EMAIL>' });
  logger.warn('API rate limit approaching', { currentRequests: 95, limit: 100 });
  logger.debug('Debug information', { debugData: { step: 1, value: 'test' } });
}

// Example 2: Error logging
export function errorLoggingExample() {
  try {
    // Simulate an error
    throw new Error('Something went wrong');
  } catch (error) {
    logger.error('Failed to process request', error, {
      userId: '123',
      action: 'processPayment',
      timestamp: new Date().toISOString()
    });
  }
}

// Example 3: API request logging
export function apiRequestLoggingExample() {
  const requestId = 'req-123';
  const userId = 'user-456';
  
  logger.info('API request started', {
    requestId,
    userId,
    endpoint: '/api/users/profile',
    method: 'GET'
  });
  
  // Simulate API processing
  setTimeout(() => {
    logger.info('API request completed', {
      requestId,
      userId,
      duration: '150ms',
      statusCode: 200
    });
  }, 150);
}

// Example 4: Authentication logging
export function authLoggingExample() {
  const userEmail = '<EMAIL>';
  const userRole = 'superdealer';
  
  logger.info('User authentication successful', {
    email: userEmail,
    role: userRole,
    loginTime: new Date().toISOString()
  });
  
  if (userRole === 'superdealer') {
    logger.info('Redirecting superdealer to dashboard', {
      email: userEmail,
      redirectUrl: '/dashboard/superdealer-groups'
    });
  }
}

// Example 5: Performance logging
export function performanceLoggingExample() {
  const startTime = Date.now();
  
  // Simulate some work
  setTimeout(() => {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    if (duration > 1000) {
      logger.warn('Slow operation detected', {
        operation: 'dataProcessing',
        duration: `${duration}ms`,
        threshold: '1000ms'
      });
    } else {
      logger.debug('Operation completed', {
        operation: 'dataProcessing',
        duration: `${duration}ms`
      });
    }
  }, 500);
}

// Run examples if this file is executed directly
if (require.main === module) {
  console.log('🚀 Running logger examples...\n');
  
  basicLoggingExample();
  errorLoggingExample();
  apiRequestLoggingExample();
  authLoggingExample();
  performanceLoggingExample();
  
  console.log('\n✅ Logger examples completed');
}
