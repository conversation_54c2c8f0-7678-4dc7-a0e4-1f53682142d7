import { buttonVariants } from '@/components/ui/button';
import Link from 'next/link';
import React from 'react';

export const metadata = {
  title: 'OC | Administration',
};

const corrections = [
  {
    name: 'EE',
    title: 'Expression écrite',
  },
  {
    name: 'E<PERSON>',
    title: 'Expression orale',
  },
] as const;

function page() {
  return (
    <div className="mx-auto grid max-w-md grid-cols-2 grid-rows-2 place-content-center gap-4">
      {corrections.map((correction) => (
        <Link
          key={correction.name}
          href={`/dashboard/administration/correction?examen=TCF&mode=${correction.name}`}
          className={buttonVariants({
            variant: 'outline',
            className: '!py-10 text-sm font-bold lg:text-base',
          })}
        >
          {correction.title} TCF
        </Link>
      ))}
      {corrections.map((correction) => (
        <Link
          key={correction.name}
          href={`/dashboard/administration/correction?examen=TEF&mode=${correction.name}`}
          className={buttonVariants({
            variant: 'outline',
            className: '!py-10 text-sm font-bold lg:text-base',
          })}
        >
          {correction.title} TEF
        </Link>
      ))}
    </div>
  );
}

export default page;
