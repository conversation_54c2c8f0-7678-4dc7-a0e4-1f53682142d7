'use client';

import { signIn, useSession, getSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from '@/components/ui/use-toast';
import logger from '@/lib/logger';

interface SignInData {
  email: string;
  password: string;
}

interface UseSignInReturn {
  signInUser: (data: SignInData, callbackUrl?: string) => Promise<void>;
  isLoading: boolean;
}

export function useSignIn(): UseSignInReturn {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const {data: session} = useSession();

  const signInUser = async (data: SignInData, callbackUrl?: string) => {
    setIsLoading(true);

    try {
      const result = await signIn('credentials', {
        email: data.email.trim(),
        password: data.password,
        redirect: false,
        callbackUrl: callbackUrl || '/',
      });

      if (result?.error) {
        toast({
          title: 'Erreur de connexion',
          description: result.error,
          variant: 'destructive',
        });
        return;
      }

      if (result?.ok) {
        toast({
          title: 'Connexion réussie',
          description: 'Redirection en cours...',
        });

        // Attendre un peu pour que la session soit mise à jour
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Récupérer la session mise à jour pour vérifier le rôle
        const updatedSession = await getSession();

        // Déterminer l'URL de redirection basée sur le rôle
        let redirectUrl: string;
        if (updatedSession?.user?.role === 'superdealer') {
          redirectUrl = '/dashboard/superdealer-groups';
          logger.info('Redirecting superdealer to dashboard', { userId: updatedSession.user._id });
        } else {
          redirectUrl = callbackUrl || '/';
        }

        try {
          router.push(redirectUrl);
          // Si ça ne marche pas après 1 seconde, forcer avec window.location
          setTimeout(() => {
            if (window.location.pathname === '/signin') {
              logger.warn('Forcing redirect with window.location', { redirectUrl });
              window.location.href = redirectUrl;
            }
          }, 1000);
        } catch (routerError) {
          logger.error('Router push failed, using window.location', routerError);
          window.location.href = redirectUrl;
        }
      }
    } catch (error) {
      logger.error('Erreur lors de la connexion:', error);
      toast({
        title: 'Erreur',
        description: "Une erreur inattendue s'est produite",
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    signInUser,
    isLoading,
  };
}
