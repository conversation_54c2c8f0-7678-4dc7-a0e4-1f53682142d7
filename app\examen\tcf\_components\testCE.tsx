'use client';
import React, { startTransition, useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import logo from '@/public/logo4.png';
import { getScore, getTestSet } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

import Image from 'next/image';
import { Loader2 } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { DialogClose } from '@radix-ui/react-dialog';
import { BUCKET_BASE_URL, GLOBAL_CE_TEST_TIME } from '@/config';
import { Resultat, Resultset, Serie } from '@/types';
import GlobalTimer from './timer';
import { saveTestCEUseCase } from '../actions';
import { EndTextBtn } from '../../_components/button';
import { toast as sonner } from 'sonner';
import { useTCFState } from '@/context/tcf';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useOfflineState } from '@/context/tcf/anonymousSate';

export default function TestCETCF({ serie }: { serie: Serie }) {
  const {
    current,
    next,
    prev,
    resulSet,
    Addset,
    // testSet,
    setresSet,
    setCurrent,
    Restart,
    setUser,
    setSerie,
    SetMode,
  } = useTCFState();
  const { setDetailSet } = useOfflineState();
  const { data: session } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);
  const [imageKey, setImageKey] = useState(0); // Pour forcer le rechargement de l'image
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set()); // Cache des images chargées
  const TimeName = `CE_TIME_${serie.libelle}_${session?.user._id}`;
  const ResName = `CE_RES_${serie.libelle}_${session?.user._id}`;
  useEffect(() => {
    SetMode('CE');
    setUser(session?.user ?? null);
    setSerie(serie);
    const PrevRes = JSON.parse(
      localStorage.getItem(ResName) ?? '[]',
    ) as Resultset[];
    if (PrevRes.length > 0) {
      setresSet(PrevRes);
    }
  }, [session, ResName]);

  const clearLocalStorage = async () => {
    window.localStorage.removeItem(TimeName);
    window.localStorage.removeItem(ResName);
  };
  const [starting, setstarting] = useState<boolean>(true);
  const questions = getTestSet(serie).CE;
  const question = questions?.[current];
  const router = useRouter();
  const time = new Date();
  time.setSeconds(time.getSeconds() + GLOBAL_CE_TEST_TIME);

  const dialog = useRef<HTMLButtonElement>(null);
  // Effet pour gérer le changement d'image
  useEffect(() => {
    const hasRealImage =
      question?.libelle && question?.libelle?.trim()?.length! > 0;

    if (!hasRealImage) {
      // Si c'est le logo, pas de chargement nécessaire et pas de changement de clé
      setImageLoading(false);
      return;
    }

    const currentImageSrc = `${BUCKET_BASE_URL}/${question?.libelle!}`;

    // Si l'image a déjà été chargée, pas besoin d'afficher le loader
    if (loadedImages.has(currentImageSrc)) {
      setImageLoading(false);
    } else {
      setImageLoading(true);
    }

    // Ne changer la clé que pour les vraies images
    setImageKey((prev) => prev + 1);
  }, [current, question?.libelle, loadedImages]);
  const onExpire = async () => {
    setstarting(false);
    if (resulSet.length > 0) {
      setIsSubmitting(true);
      if (!session?.user) {
        setDetailSet(null);
        const data: Resultat = {
          _id: 'random_id',
          serie: serie,
          payload: JSON.stringify(resulSet, null, 0),
          resultat: getScore(getTestSet(serie), resulSet).CE ?? 0,
          createdAt: new Date().toISOString(),
          user: null,
        };
        setDetailSet(data);
        clearLocalStorage();
        Restart();
        router.push(`/resultat/TCF/CE`);
      } else {
        sonner.promise(
          saveTestCEUseCase({
            resulset: resulSet,
            serieId: serie._id,
            resultat: getScore(getTestSet(serie), resulSet).CE ?? 0,
          }),
          {
            loading: 'Correction du test...',
            success: () => {
              clearLocalStorage();
              Restart();
              return 'Votre test a été enregistré avec succès';
            },
            error: (error) => {
              setIsSubmitting(false);
              return error.message === 'Failed to fetch'
                ? 'Verifier votre connexion'
                : error.message;
            },
            closeButton: true,
            duration: 1000 * 20,
          },
        );
      }
    } else {
      Restart();
      await clearLocalStorage();
      router.push(`/examen/tcf/${serie.libelle}`);
      sonner.warning("Vous n'avez répondu à aucune question", {
        duration: 1000 * 10,
        closeButton: true,
        description: 'Ce test sera annulé',
      });
    }
  };

  const LETTERS = ['A', 'B', 'C', 'D'] as const;
  return (
    <div className="relative flex w-full flex-col gap-5">
      <div className="fixed inset-x-0 top-0 z-40 flex w-full flex-col items-center justify-around border-b bg-white p-2 md:flex-row">
        <div className="flex items-center gap-px">
          <GlobalTimer
            key={TimeName}
            mode="CE"
            serieLib={serie.libelle}
            recordTime={true}
            expiryTimestamp={time}
            onExpire={() => onExpire()}
            starting={starting}
            totalduration={GLOBAL_CE_TEST_TIME}
          />
          <EndTextBtn onClick={() => onExpire()}>
            <button
              title="stop"
              disabled={isSubmitting}
              className="flex aspect-video items-center justify-center rounded-sm bg-red-500 p-1.5 md:hidden md:p-3"
            >
              <p className="font-semibold text-white">Fin</p>
            </button>
          </EndTextBtn>
        </div>
        <div className="p-2 tracking-wide md:tracking-wider">
          <p className="text-center text-xs font-semibold md:text-base">
            TCF: Objectif-Canada || Série {serie.libelle} || Compréhension
            écrite.
          </p>
        </div>
        <EndTextBtn onClick={() => onExpire()}>
          <button
            disabled={isSubmitting}
            title="stop"
            className="hidden aspect-video items-center justify-center rounded-sm bg-red-500 p-3 md:flex"
          >
            <p className="font-semibold text-white">Fin</p>
          </button>
        </EndTextBtn>
      </div>
      <div className="mt-20 flex gap-2">
        <div className="flex-1">
          <div className="relative mx-auto mb-3 h-[40vh] w-full rounded-sm border md:h-[350px] md:w-full md:max-w-2xl">
            {/* Indicateur de chargement */}
            {imageLoading && (
              <div className="absolute inset-0 z-10 flex items-center justify-center bg-gray-100/80 backdrop-blur-sm">
                <div className="flex flex-col items-center gap-2">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                  <p className="text-sm text-gray-600">
                    Chargement de l'image...
                  </p>
                </div>
              </div>
            )}
            <Image
              key={imageKey} // Force le rechargement de l'image
              alt="image-question"
              src={
                question?.libelle && question?.libelle?.trim()?.length! > 0
                  ? `${BUCKET_BASE_URL}/${question?.libelle!}`
                  : logo
              }
              fill
              sizes="(min-width: 1024px) 1024px, 100vw"
              priority
              className={`object-contain transition-opacity duration-300 ${
                imageLoading ? 'opacity-0' : 'opacity-100'
              }`}
              placeholder={
                question?.libelle && question?.libelle?.trim()?.length! > 0
                  ? 'empty'
                  : 'blur'
              }
              onLoad={() => {
                const hasRealImage =
                  question?.libelle && question?.libelle?.trim()?.length! > 0;
                if (hasRealImage) {
                  const currentImageSrc = `${BUCKET_BASE_URL}/${question?.libelle!}`;
                  setLoadedImages((prev) => new Set(prev).add(currentImageSrc));
                }
                setImageLoading(false);
              }}
              onError={() => {
                const hasRealImage =
                  question?.libelle && question?.libelle?.trim()?.length! > 0;
                if (hasRealImage) {
                  const currentImageSrc = `${BUCKET_BASE_URL}/${question?.libelle!}`;
                  setLoadedImages((prev) => new Set(prev).add(currentImageSrc));
                }
                setImageLoading(false);
              }}
            />
          </div>
          <div className="mx-auto flex w-full flex-col gap-3 overflow-hidden rounded-lg border-2 border-t-0 border-blue-400 md:mt-0">
            <div className="flex w-full items-center gap-5 bg-blue-500 p-1">
              <span
                onClick={() => {
                  if (window.outerWidth < 485) dialog.current?.click();
                }}
                className="flex h-10 w-16 cursor-pointer items-center justify-center rounded-md bg-white p-4 text-base font-bold md:text-xl"
              >
                {question?.numero}
              </span>
              <h1 className="text-sm font-medium tracking-widest text-white md:text-base">
                {question?.consigne}
              </h1>
            </div>
            <div className="flex flex-col items-start justify-center gap-2 rounded-sm bg-white p-1">
              {question?.suggestions.map((reponse, i) => (
                <div
                  key={reponse._id}
                  className="group flex cursor-pointer items-center gap-5 p-1"
                  onClick={() => {
                    startTransition(() => {
                      Addset({
                        questionId: question?.numero!,
                        resId: reponse._id,
                      });
                    });
                  }}
                >
                  <span
                    className={`group-hover:bg-blue-500 ${
                      resulSet.find((q) => q.questionId == question?.numero)
                        ?.resId === reponse._id
                        ? 'bg-blue-500'
                        : 'bg-gray-400'
                    } flex h-6 w-6 items-center justify-center rounded-full p-4 text-sm font-semibold text-white`}
                  >
                    {LETTERS.at(i)}
                  </span>
                  <h1
                    className={`text-sm font-medium tracking-widest md:text-base ${
                      resulSet.find((q) => q.questionId == question?.numero)
                        ?.resId === reponse._id
                        ? 'font-semibold text-blue-500'
                        : ''
                    }`}
                  >
                    {reponse.text}
                  </h1>
                </div>
              ))}
            </div>
          </div>
          <div className="mx-auto flex w-full items-center justify-end gap-4">
            <Button
              variant={'outline'}
              className="my-2 rounded-l-full rounded-r-full"
              disabled={current == 0}
              onClick={() => {
                startTransition(() => {
                  prev();
                });
              }}
            >
              Précédent
            </Button>
            {current + 1 == questions?.length ? (
              <Button
                className="my-2 rounded-l-full rounded-r-full"
                variant={'destructive'}
                onClick={() => onExpire()}
              >
                Fin
              </Button>
            ) : (
              <Button
                className="my-2 rounded-l-full rounded-r-full"
                onClick={() => {
                  startTransition(() => {
                    next();
                  });
                }}
              >
                Suivant
              </Button>
            )}
          </div>
        </div>
        <div className="hidden h-fit md:block">
          <div className="row13 grid grid-cols-3 gap-1.5">
            {questions?.map((q, i) => (
              <Button
                disabled={isSubmitting}
                key={`${q.numero}-${i}`}
                onClick={() => {
                  startTransition(() => {
                    setCurrent(q.numero % 40);
                  });
                }}
                className={`w-full rounded-none ${
                  resulSet.find((r) => r.questionId == q.numero)
                    ? ''
                    : 'bg-emerald-300'
                }`}
              >
                {q.numero}
              </Button>
            ))}
          </div>
        </div>
      </div>

      <Dialog>
        <DialogTrigger asChild>
          <Button variant="outline" className="hidden" ref={dialog}>
            Edit Profile
          </Button>
        </DialogTrigger>
        <DialogContent className="flex w-[100px] flex-col">
          <DialogHeader>
            <DialogTitle>{''}</DialogTitle>
            <DialogDescription>
              <ScrollArea className="mx-auto h-[50vh] max-h-full w-fit rounded-md border">
                <div className="">
                  {questions?.map((q, i) => (
                    <span key={`dialog-${q.numero}-${i}`}>
                      <DialogClose
                        disabled={isSubmitting}
                        onClick={() => {
                          setCurrent(q.numero % 40);
                        }}
                        className={`w-full rounded-none p-4 text-base font-medium text-white ${
                          resulSet.find((r) => r.questionId == q.numero)
                            ? 'bg-blue-500'
                            : 'bg-emerald-300'
                        } `}
                      >
                        {q.numero}
                      </DialogClose>
                      <Separator className="w-full" />
                    </span>
                  ))}
                </div>
              </ScrollArea>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </div>
  );
}
