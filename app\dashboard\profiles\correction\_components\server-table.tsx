import { ExpressionTest, ExpressionTestResult } from '@/types';
import ExpressionList from './list';
import { Skeleton } from '@/components/ui/skeleton';
import axios from 'axios';
import { getAuthSession } from '@/lib/auth';
import logger from '@/lib/logger';
const baseUrl =
  process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || '';
async function getTestsEE(token: string): Promise<ExpressionTestResult[]> {
  const { data } = await axios.get<{ tests: ExpressionTest[] }>(
    `${baseUrl}/api/dealer/profiles/testsee-en-cours`,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    },
  );
  return data.tests.map((test) => ({ ...test, type: 'EE' }));
}

async function getTestsEO(token: string): Promise<ExpressionTestResult[]> {
  const { data } = await axios.get<{ tests: ExpressionTest[] }>(
    `${baseUrl}/api/dealer/profiles/testseo-en-cours`,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    },
  );
  return data.tests.map((test) => ({ ...test, type: 'EO' }));
}

async function getTestsEESuperdealer(
  token: string,
  groupId?: string,
): Promise<ExpressionTestResult[]> {
  if (!groupId) return [];
  const { data } = await axios.get<{ tests: ExpressionTest[] }>(
    `${baseUrl}/api/eeTest/tests/pending/by-group/${groupId}`,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    },
  );
  return data.tests.map((test) => ({ ...test, type: 'EE' }));
}

async function getTestsEOSuperdealer(
  token: string,
  groupId?: string,
): Promise<ExpressionTestResult[]> {
  if (!groupId) return [];
  const { data } = await axios.get<{ tests: ExpressionTest[] }>(
    `${baseUrl}/api/eoTest/tests/pending/by-group/${groupId}`,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    },
  );
  return data.tests.map((test) => ({ ...test, type: 'EO' }));
}

export const WritingExpression = async ({
  groupId,
}: {
  groupId: string | undefined;
}) => {
  const session = await getAuthSession();
  const token = session?.user.accessToken || '';
  const data =
    session?.user.role! == 'dealer'
      ? await getTestsEE(token)
      : await getTestsEESuperdealer(token, groupId);
  return <ExpressionList data={data} />;
};

export const OralExpression = async ({
  groupId,
}: {
  groupId: string | undefined;
}) => {
  logger.info('OralExpression', { groupId });
  const session = await getAuthSession();
  const token = session?.user.accessToken || '';
  const data =
    session?.user.role! == 'dealer'
      ? await getTestsEO(token)
      : await getTestsEOSuperdealer(token, groupId);
  return <ExpressionList data={data} />;
};

export const ExpressionLoader = () => {
  return <Skeleton className="h-[100px] w-full rounded-md" />;
};
