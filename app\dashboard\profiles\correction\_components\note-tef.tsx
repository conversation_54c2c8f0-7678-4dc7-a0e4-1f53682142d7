'use client';

import { useCorrectionStore } from '@/context/correction';
import { cn, getNiveau } from '@/lib/utils';

const Note = () => {
  const noteTaskOne = useCorrectionStore((state) => state.noteTaskOne) ?? 0;
  const noteTaskTwo = useCorrectionStore((state) => state.noteTaskTwo) ?? 0;
  const note = (noteTaskOne + noteTaskTwo) * 29;
  return (
    <>
      <p className="flex items-end gap-0.5">
        <span className="text-3xl">{note.toString().padStart(3, '0')}</span>
        <span>/699</span>
      </p>{' '}
      -{' '}
      <span
        className={cn('text-red-500', {
          'text-green-500': note >= 450,
        })}
      >
        {getNiveau(note)}
      </span>
    </>
  );
};

export default Note;
