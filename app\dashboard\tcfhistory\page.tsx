'use client';
import { useQuery } from '@tanstack/react-query';
import TCFContentDashboard from './Content';
import { getComprehensionResult, getExpressionResult } from '@/lib/utils';
import HistoryLoader from '../_components/loader-history';
import DashboardNavigation from '../_components/navigation';
import { useSession } from 'next-auth/react';
import Partners from '@/components/partners';

export default function Page() {
  const { data: session } = useSession();
  const { data, isLoading, isError } = useQuery(['user-tcf-data'], async () => {
    const [CERes, CORes, EERes, EORes] = await Promise.all([
      getComprehensionResult('CE'),
      getComprehensionResult('CO'),
      getExpressionResult('EE'),
      getExpressionResult('EO'),
    ]);
    return { CERes, CORes, EERes, EORes };
  });

  if (isError) {
    return (
      <div className="grid place-content-center">
        <h1>Failed to load data. Please try again later.</h1>
      </div>
    );
  }

  if (isLoading || !data) {
    return <HistoryLoader />;
  }

  const { CERes, CORes, EERes, EORes } = data!;
  const dataC = [
    ...(CERes?.data || []).map((d) => ({ ...d, type: 'CE' })),
    ...(CORes?.data || []).map((d) => ({ ...d, type: 'CO' })),
  ];

  const error = CERes?.error || CORes?.error || EERes?.error || EORes?.error;

  return (
    <>
      <DashboardNavigation />

      {dataC.length || EERes?.data || EORes?.data ? (
        <TCFContentDashboard
          dataC={dataC.filter((d) => d.serie)}
          dataE={EERes?.data.filter((d) => d.serie)}
          dataO={EORes?.data.filter((d) => d.serie)}
          error={error}
          remains={session?.user.remains?.remainTCF}
        />
      ) : null}
      <Partners />
    </>
  );
}
