// /**
//  * Tests for the custom logger
//  * This file can be run to verify that the logger works correctly
//  */

// import logger from '../logger';

// // Mock console methods to capture their calls
// const originalConsole = {
//   log: console.log,
//   info: console.info,
//   warn: console.warn,
//   error: console.error,
//   debug: console.debug,
// };

// const mockConsole = {
//   log: jest.fn(),
//   info: jest.fn(),
//   warn: jest.fn(),
//   error: jest.fn(),
//   debug: jest.fn(),
// };

// // Test function to verify logger behavior
// export function testLogger() {
//   console.log('🧪 Testing custom logger...');
  
//   // Test in development mode (should log to console)
//   process.env.NODE_ENV = 'development';
  
//   // Replace console methods with mocks
//   Object.assign(console, mockConsole);
  
//   try {
//     // Test different log levels
//     logger.log('Test log message', { test: true });
//     logger.info('Test info message', { test: true });
//     logger.warn('Test warning message', { test: true });
//     logger.error('Test error message', new Error('Test error'), { test: true });
//     logger.debug('Test debug message', { test: true });
    
//     console.log('✅ Logger test completed successfully');
//     console.log('📊 Console calls made:');
//     console.log(`  - log: ${mockConsole.log.mock.calls.length}`);
//     console.log(`  - info: ${mockConsole.info.mock.calls.length}`);
//     console.log(`  - warn: ${mockConsole.warn.mock.calls.length}`);
//     console.log(`  - error: ${mockConsole.error.mock.calls.length}`);
//     console.log(`  - debug: ${mockConsole.debug.mock.calls.length}`);
    
//   } catch (error) {
//     console.error('❌ Logger test failed:', error);
//   } finally {
//     // Restore original console methods
//     Object.assign(console, originalConsole);
//   }
// }

// // Run test if this file is executed directly
// if (require.main === module) {
//   testLogger();
// }
